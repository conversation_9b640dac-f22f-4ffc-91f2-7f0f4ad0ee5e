"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/modal";
import Label from "@/components/form/Label";
import Input from "@/components/form/input/InputField";
import Button from "@/components/ui/button/Button";
import { updatePatient } from "@/app/actions/patient";

interface Patient {
  id: number;
  nom: string;
  prenom: string;
  telephone?: string;
  date_naissance: string;
  numero_patient: string;
  genre: string;
  adresse?: string;
  antecedent?: string;
  diagnostic?: string;
  photo?: string;
  consultation?: string;
  consultation_specialisee?: string;
  ct_sim?: string;
  debut_traitement?: string;
  fin_traitement?: string;
  rdv_traitement?: string;
  technique_irradiation?: string;
  dose_totale?: number;
  dose_fraction?: number;
  lieu_naissance?: string;
  wilaya?: string;
  cni?: string;
  service_correspondant?: string;
  localisation_cancer?: string;
  cree_le?: string;
  nombre_seances?: number;
}

interface EditPatientModalProps {
  isOpen: boolean;
  onClose: () => void;
  patient: Patient;
  onUpdate: (updatedPatient: Patient) => void;
}

export default function EditPatientModal({
  isOpen,
  onClose,
  patient,
  onUpdate,
}: EditPatientModalProps) {
  const [formData, setFormData] = useState<Partial<Patient>>({
    nom: patient.nom,
    prenom: patient.prenom,
    telephone: patient.telephone || '',
    date_naissance: patient.date_naissance ? new Date(patient.date_naissance).toISOString().split('T')[0] : '',
    numero_patient: patient.numero_patient,
    genre: patient.genre,
    adresse: patient.adresse || '',
    lieu_naissance: patient.lieu_naissance || '',
    wilaya: patient.wilaya || '',
    localisation_cancer: patient.localisation_cancer || '',
    service_correspondant: patient.service_correspondant || '',
    cni: patient.cni || '',
    antecedent: patient.antecedent || '',
    diagnostic: patient.diagnostic || '',
    technique_irradiation: patient.technique_irradiation || '',
    dose_totale: patient.dose_totale || undefined,
    dose_fraction: patient.dose_fraction || undefined,
    consultation: patient.consultation ? new Date(patient.consultation).toISOString().split('T')[0] : '',
    consultation_specialisee: patient.consultation_specialisee ? new Date(patient.consultation_specialisee).toISOString().split('T')[0] : '',
    ct_sim: patient.ct_sim ? new Date(patient.ct_sim).toISOString().split('T')[0] : '',
    debut_traitement: patient.debut_traitement ? new Date(patient.debut_traitement).toISOString().split('T')[0] : '',
    fin_traitement: patient.fin_traitement ? new Date(patient.fin_traitement).toISOString().split('T')[0] : '',
    rdv_traitement: patient.rdv_traitement ? new Date(patient.rdv_traitement).toISOString().split('T')[0] : '',
    nombre_seances: patient.nombre_seances || undefined
  });
  const [isLoading, setIsLoading] = useState(false);
  const [photoFile, setPhotoFile] = useState<File | null>(null);

  const calculateFinTraitement = (debut: string, nombreSeances: number) => {
    if (!debut || !nombreSeances) return '';
    
    const dateDebut = new Date(debut);
    let joursAjoutes = 0;
    let joursTravailles = 0;
    
    while (joursTravailles < nombreSeances) {
      dateDebut.setDate(dateDebut.getDate() + 1);
      const jourSemaine = dateDebut.getDay();
      
      // Si ce n'est pas un vendredi (5) ou un samedi (6)
      if (jourSemaine !== 5 && jourSemaine !== 6) {
        joursTravailles++;
      }
      joursAjoutes++;
    }
    
    return dateDebut.toISOString().split('T')[0];
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      const numValue = value ? parseFloat(value) : null;
      setFormData((prev) => {
        const newData = { ...prev, [name]: numValue };
        
        // Si on modifie le nombre de séances ou la date de début, recalculer la date de fin
        if (name === 'nombre_seances' || name === 'debut_traitement') {
          const debut = name === 'debut_traitement' ? value : prev.debut_traitement;
          const seances = name === 'nombre_seances' ? numValue : prev.nombre_seances;
          
          if (debut && seances) {
            newData.fin_traitement = calculateFinTraitement(debut, seances);
          }
        }
        
        return newData;
      });
    } else {
      setFormData((prev) => {
        const newData = { ...prev, [name]: value };
        
        // Si on modifie la date de début, recalculer la date de fin
        if (name === 'debut_traitement' && prev.nombre_seances) {
          newData.fin_traitement = calculateFinTraitement(value, prev.nombre_seances);
        }
        
        return newData;
      });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setPhotoFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formDataToSend = new FormData();

      // Ajouter tous les champs non nuls au FormData
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== null && value !== undefined && key !== 'id' && key !== 'cree_le') {
          // Convertir les nombres en chaînes
          if (typeof value === 'number') {
            formDataToSend.append(key, value.toString());
          } else {
            formDataToSend.append(key, value);
          }
        }
      });

      // Ajouter la photo si elle existe
      if (photoFile) {
        formDataToSend.append('photo', photoFile);
      }

      const result = await updatePatient(patient.id, formDataToSend);

      if (!result.success) {
        throw new Error(result.error || 'Erreur lors de la mise à jour');
      }

      onUpdate(result.data);
      onClose();
      alert('Patient mis à jour avec succès !');
    } catch (error) {
      console.error('Erreur:', error);
      alert(error instanceof Error ? error.message : 'Erreur lors de la mise à jour du patient');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Nettoyage de l'URL de l'objet lors du démontage du composant
    return () => {
      if (photoFile) {
        URL.revokeObjectURL(URL.createObjectURL(photoFile));
      }
    };
  }, [photoFile]);

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      className="max-w-[90%] md:max-w-[800px] m-4 max-h-[90vh] overflow-y-auto"
    >
      <div className="p-4 md:p-6">
        <h2 className="text-2xl font-bold mb-6">Modifier le patient</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Informations personnelles */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Informations personnelles</h3>
              
              <div>
                <Label htmlFor="nom">Nom</Label>
                <Input
                  id="nom"
                  name="nom"
                  value={formData.nom}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label htmlFor="prenom">Prénom</Label>
                <Input
                  id="prenom"
                  name="prenom"
                  value={formData.prenom}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label htmlFor="numero_patient">Numéro patient</Label>
                <Input
                  id="numero_patient"
                  name="numero_patient"
                  value={formData.numero_patient}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label htmlFor="telephone">Téléphone</Label>
                <Input
                  id="telephone"
                  name="telephone"
                  value={formData.telephone || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="date_naissance">Date de naissance</Label>
                <Input
                  id="date_naissance"
                  name="date_naissance"
                  type="date"
                  value={formData.date_naissance || ''}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <Label htmlFor="genre">Genre</Label>
                <select
                  id="genre"
                  name="genre"
                  value={formData.genre}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="Homme">Homme</option>
                  <option value="Femme">Femme</option>
                </select>
              </div>

              <div>
                <Label htmlFor="adresse">Adresse</Label>
                <Input
                  id="adresse"
                  name="adresse"
                  value={formData.adresse || ''}
                  onChange={handleChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Lieu de naissance</label>
                  <input
                    type="text"
                    name="lieu_naissance"
                    value={formData.lieu_naissance || ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Wilaya</label>
                  <input
                    type="text"
                    name="wilaya"
                    value={formData.wilaya || ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">CNI</label>
                  <input
                    type="text"
                    name="cni"
                    value={formData.cni || ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Service correspondant</label>
                  <input
                    type="text"
                    name="service_correspondant"
                    value={formData.service_correspondant || ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Informations médicales */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Informations médicales</h3>
              
              <div>
                <Label htmlFor="antecedent">Antécédents</Label>
                <textarea
                  id="antecedent"
                  name="antecedent"
                  value={formData.antecedent || ''}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="diagnostic">Diagnostic</Label>
                <textarea
                  id="diagnostic"
                  name="diagnostic"
                  value={formData.diagnostic || ''}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="technique_irradiation">Technique d'irradiation</Label>
                <Input
                  id="technique_irradiation"
                  name="technique_irradiation"
                  value={formData.technique_irradiation || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="dose_totale">Dose totale (Gy)</Label>
                <Input
                  id="dose_totale"
                  name="dose_totale"
                  type="number"
                  step={0.01}
                  value={formData.dose_totale ?? ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="dose_fraction">Dose par fraction (Gy)</Label>
                <Input
                  id="dose_fraction"
                  name="dose_fraction"
                  type="number"
                  step={0.01}
                  value={formData.dose_fraction ?? ''}
                  onChange={handleChange}
                />
              </div>

              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Informations médicales</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Antécédents</label>
                    <textarea
                      name="antecedent"
                      value={formData.antecedent || ''}
                      onChange={handleChange}
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Diagnostic</label>
                    <textarea
                      name="diagnostic"
                      value={formData.diagnostic || ''}
                      onChange={handleChange}
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Localisation du cancer</label>
                    <input
                      type="text"
                      name="localisation_cancer"
                      value={formData.localisation_cancer || ''}
                      onChange={handleChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Dates */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Dates importantes</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="consultation">Consultation initiale</Label>
                <Input
                  id="consultation"
                  name="consultation"
                  type="date"
                  value={formData.consultation || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="consultation_specialisee">Consultation spécialisée</Label>
                <Input
                  id="consultation_specialisee"
                  name="consultation_specialisee"
                  type="date"
                  value={formData.consultation_specialisee || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="ct_sim">CT Sim</Label>
                <Input
                  id="ct_sim"
                  name="ct_sim"
                  type="date"
                  value={formData.ct_sim || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="debut_traitement">Début traitement</Label>
                <Input
                  id="debut_traitement"
                  name="debut_traitement"
                  type="date"
                  value={formData.debut_traitement || ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="nombre_seances">Nombre de séances</Label>
                <Input
                  id="nombre_seances"
                  name="nombre_seances"
                  type="number"
                  min="1"
                  value={formData.nombre_seances ?? ''}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label htmlFor="fin_traitement">Fin traitement (calculée)</Label>
                <Input
                  id="fin_traitement"
                  name="fin_traitement"
                  type="date"
                  value={formData.fin_traitement || ''}
                  onChange={handleChange}
                  readOnly
                  className="bg-gray-50"
                />
              </div>

              <div>
                <Label htmlFor="rdv_traitement">RDV traitement</Label>
                <Input
                  id="rdv_traitement"
                  name="rdv_traitement"
                  type="date"
                  value={formData.rdv_traitement || ''}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Dates importantes</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Rendez-vous traitement</label>
                  <input
                    type="datetime-local"
                    name="rdv_traitement"
                    value={formData.rdv_traitement ? new Date(formData.rdv_traitement).toISOString().slice(0, 16) : ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Consultation spécialisée</label>
                  <input
                    type="datetime-local"
                    name="consultation_specialisee"
                    value={formData.consultation_specialisee ? new Date(formData.consultation_specialisee).toISOString().slice(0, 16) : ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Photo */}
          <div>
            <Label htmlFor="photo">Photo</Label>
            <div className="flex items-center gap-4">
              {photoFile ? (
                <img
                  src={URL.createObjectURL(photoFile)}
                  alt="Nouvelle photo"
                  className="w-20 h-20 object-cover rounded-full"
                />
              ) : formData.photo ? (
                <img
                  src={formData.photo}
                  alt="Photo actuelle"
                  className="w-20 h-20 object-cover rounded-full"
                />
              ) : (
                <div className="w-20 h-20 bg-gray-300 rounded-full" />
              )}
              <input
                type="file"
                id="photo"
                name="photo"
                onChange={handleFileChange}
                accept="image/*"
                className="hidden"
              />
              <label
                htmlFor="photo"
                className="cursor-pointer bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
              >
                {formData.photo || photoFile ? 'Changer la photo' : 'Ajouter une photo'}
              </label>
            </div>
          </div>

          {/* Boutons */}
          <div className="flex justify-end gap-4">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <Button disabled={isLoading}>
              {isLoading ? 'Mise à jour...' : 'Mettre à jour'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}






