generator client {
  provider = "prisma-client-js"
  output   = "../../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========== ENUMS ==========
enum Role {
  super_admin
  admin
  medecin
}

enum Genre {
  Homme
  Femme
  Autre
}

enum StatutFileAttente {
  EN_ATTENTE
  EN_COURS
}

enum TypeConsultation {
  INITIAL
  STANDARD
  SPECIALISEE
}

enum ActionHistorique {
  AJOUT_FILE
  CONSULTATION
  FIN_TRAITEMENT
  AUTRE
}

// ========== MODELS ==========

model User {
  id           Int      @id @default(autoincrement())
  nom          String
  prenom       String
  email        String   @unique
  mot_de_passe String
  role         Role
  photo        String?
  cree_le      DateTime @default(now())

  // Relations
  medecin      Medecin?
}

model Medecin {
  id          Int       @id @default(autoincrement())
  specialite  String
  telephone   String?
  userId      Int       @unique
  user        User      @relation(fields: [userId], references: [id])

  // Relations
  patients       Patient[]   @relation("MedecinPatients")
  fileAttente    FileAttente[]
  consultations  Consultation[]
  messages       Message[]   @relation("MedecinMessages")
  historique     HistoriqueConsultation[]
}

model Patient {
  id                       Int      @id @default(autoincrement())
  numero_patient           String   @unique
  nom                      String
  prenom                   String
  telephone                String?
  date_naissance           DateTime
  adresse                  String?
  genre                    Genre
  medecinId                Int?
  antecedent               String?
  diagnostic               String?
  photo                    String?
  consultation             DateTime?
  consultation_specialisee DateTime?
  ct_sim                   DateTime?
  debut_traitement         DateTime?
  fin_traitement           DateTime?
  rdv_traitement           DateTime?
  technique_irradiation    String?
  dose_totale              Float?
  dose_fraction            Float?
  cree_le                  DateTime @default(now())

  // Relations
  medecin       Medecin?                 @relation("MedecinPatients", fields: [medecinId], references: [id])
  fileAttentes  FileAttente[]
  consultations Consultation[]
  messages      Message[]                @relation("PatientMessages")
  historique    HistoriqueConsultation[]
}

model FileAttente {
  id         Int               @id @default(autoincrement())
  patientId  Int
  medecinId  Int
  statut     StatutFileAttente  @default(EN_ATTENTE)
  ordre      Int
  ajoute_le  DateTime           @default(now())

  // Relations
  patient   Patient @relation(fields: [patientId], references: [id])
  medecin   Medecin  @relation(fields: [medecinId], references: [id])

  // Contrainte unique pour éviter les doublons (un patient ne peut être qu'une fois dans la file d'attente d'un médecin avec le même statut)
  @@unique([patientId, medecinId, statut])
}

model Consultation {
  id          Int              @id @default(autoincrement())
  patientId   Int
  medecinId   Int
  date        DateTime          @default(now())
  debut       DateTime          @default(now())
  fin         DateTime?
  diagnostic  String?
  traitement  String?
  notes       String?
  type        TypeConsultation  @default(STANDARD)
  cree_le     DateTime          @default(now())

  // Relations
  patient     Patient  @relation(fields: [patientId], references: [id])
  medecin     Medecin  @relation(fields: [medecinId], references: [id])
}

model Message {
  id        Int      @id @default(autoincrement())
  contenu   String
  date      DateTime @default(now())
  type      String   @default("observation")
  medecinId Int
  patientId Int

  // Relations
  medecin   Medecin  @relation("MedecinMessages", fields: [medecinId], references: [id])
  patient   Patient  @relation("PatientMessages", fields: [patientId], references: [id])
}

model HistoriqueConsultation {
  id         Int               @id @default(autoincrement())
  patientId  Int
  medecinId  Int
  action     ActionHistorique
  date       DateTime           @default(now())

  // Relations
  patient    Patient @relation(fields: [patientId], references: [id])
  medecin    Medecin  @relation(fields: [medecinId], references: [id])
}

