import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Patient, Consultation, Genre } from '@prisma/client';

interface PatientWithConsultations extends Omit<Patient, 'date_naissance' | 'genre'> {
  consultations: Consultation[];
  date_naissance: Date | null;
  genre: Genre | null;
  wilaya: string | null;
  localisation_cancer: string | null;
  debut_traitement: Date | null;
  fin_traitement: Date | null;
}

export async function GET() {
  try {
    console.log('Fetching patients data...');
    const patients = await prisma.patient.findMany({
      include: {
        consultations: true,
      },
    }) as PatientWithConsultations[];

    console.log(`Found ${patients.length} patients`);

    // Statistiques par wilaya
    const wilayaStats = patients.reduce((acc: { [key: string]: number }, patient) => {
      if (patient.wilaya) {
        acc[patient.wilaya] = (acc[patient.wilaya] || 0) + 1;
      }
      return acc;
    }, {});

    // Statistiques par localisation du cancer
    const localisationStats = patients.reduce((acc: { [key: string]: number }, patient) => {
      if (patient.localisation_cancer) {
        acc[patient.localisation_cancer] = (acc[patient.localisation_cancer] || 0) + 1;
      }
      return acc;
    }, {});

    // Statistiques par genre
    const genreStats = patients.reduce((acc: { [key: string]: number }, patient) => {
      if (patient.genre) {
        acc[patient.genre] = (acc[patient.genre] || 0) + 1;
      }
      return acc;
    }, {});

    // Statistiques par âge
    const ageStats = patients.reduce((acc: { [key: string]: number }, patient) => {
      if (patient.date_naissance) {
        const age = Math.floor((new Date().getTime() - new Date(patient.date_naissance).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
        const ageGroup = Math.floor(age / 10) * 10;
        const ageRange = `${ageGroup}-${ageGroup + 9}`;
        acc[ageRange] = (acc[ageRange] || 0) + 1;
      }
      return acc;
    }, {});

    // Calcul de la durée moyenne des traitements
    const dureesTraitement = patients
      .filter(patient => patient.debut_traitement && patient.fin_traitement)
      .map(patient => {
        const debut = new Date(patient.debut_traitement!);
        const fin = new Date(patient.fin_traitement!);
        return Math.ceil((fin.getTime() - debut.getTime()) / (1000 * 60 * 60 * 24));
      });

    const dureeMoyenne = dureesTraitement.length > 0
      ? Math.round(dureesTraitement.reduce((a: number, b: number) => a + b, 0) / dureesTraitement.length)
      : 0;

    // Statistiques des consultations par mois
    const consultationsParMois = patients.reduce((acc: { [key: string]: number }, patient) => {
      patient.consultations.forEach(consultation => {
        const mois = format(new Date(consultation.date), "MMMM yyyy", { locale: fr });
        acc[mois] = (acc[mois] || 0) + 1;
      });
      return acc;
    }, {});

    const stats = {
      wilayaStats,
      localisationStats,
      genreStats,
      ageStats,
      dureeMoyenne,
      consultationsParMois,
    };

    console.log('Stats calculated:', stats);

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error in stats API:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des statistiques' },
      { status: 500 }
    );
  }
} 