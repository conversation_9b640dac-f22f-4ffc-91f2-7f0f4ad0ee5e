import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // 1. Création d'un médecin
  const medecin = await prisma.user.create({
    data: {
      nom: '<PERSON><PERSON>',
      prenom: '<PERSON>',
      email: '<EMAIL>',
      mot_de_passe: 'azerty123',
      role: 'medecin',
      specialite: 'Radiothérapie',
      telephone: '**********',
      photo: null
    }
  });

  // 2. Création d’un patient
  const patient = await prisma.patient.create({
    data: {
      numero_patient: 'P001',
      nom: '<PERSON><PERSON>',
      prenom: 'Sara',
      telephone: '**********',
      date_naissance: new Date('1985-03-15'),
      adresse: 'Oran',
      genre: 'Femme',
      medecin: {
        connect: { id: medecin.id }
      },
      antecedent: 'Hypertension',
      diagnostic: 'Cancer du sein',
      photo: null,
      consultation: new Date(),
      consultation_specialisee: null,
      ct_sim: null,
      debut_traitement: null,
      fin_traitement: null,
      rdv_traitement: null,
      technique_irradiation: null,
      dose_totale: null,
      dose_fraction: null
    }
  });

  // 3. Ajout à la file d’attente
  await prisma.fileAttente.create({
    data: {
      patient: { connect: { id: patient.id } },
      medecin: { connect: { id: medecin.id } },
      statut: 'EN_ATTENTE',
      ordre: 1
    }
  });

  // 4. Message du médecin
  await prisma.message.create({
    data: {
      contenu: 'Observation initiale : patient stable.',
      medecin: { connect: { id: medecin.id } },
      patient: { connect: { id: patient.id } },
      type: 'observation'
    }
  });

  // 5. Historique de consultation
  await prisma.historiqueConsultation.create({
    data: {
      patient: { connect: { id: patient.id } },
      medecin: { connect: { id: medecin.id } },
      action: 'AJOUT_FILE'
    }
  });

  console.log('✅ Base de données seedée avec succès');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
