'use client';

import { useState, useEffect, ReactElement } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend } from "recharts";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { Patient, Consultation, Genre } from "@prisma/client";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

interface PatientWithConsultations extends Omit<Patient, 'date_naissance' | 'genre'> {
  consultations: Consultation[];
  date_naissance: Date | null;
  genre: Genre | null;
  wilaya: string | null;
  localisation_cancer: string | null;
  debut_traitement: Date | null;
  fin_traitement: Date | null;
}

interface StatsData {
  wilayaStats: { [key: string]: number };
  localisationStats: { [key: string]: number };
  genreStats: { [key: string]: number };
  ageStats: { [key: string]: number };
  dureeMoyenne: number;
  consultationsParMois: { [key: string]: number };
}

interface PieLabelProps {
  name: string;
  percent: number;
}

type ChartType = 'wilaya' | 'localisation' | 'genre' | 'age' | 'consultations';

interface ChartOption {
  value: ChartType;
  label: string;
  type: 'bar' | 'pie';
}

const CHART_OPTIONS: ChartOption[] = [
  { value: 'wilaya', label: 'Patients par wilaya', type: 'bar' },
  { value: 'localisation', label: 'Localisations du cancer', type: 'bar' },
  { value: 'genre', label: 'Répartition par genre', type: 'pie' },
  { value: 'age', label: 'Répartition par âge', type: 'bar' },
  { value: 'consultations', label: 'Consultations par mois', type: 'bar' },
];

async function getStats(): Promise<StatsData> {
  const response = await fetch('/api/stats');
  if (!response.ok) {
    throw new Error('Failed to fetch stats');
  }
  return response.json();
}

function renderChart(type: ChartType, data: any[]): ReactElement {
  console.log('Rendering chart:', type, data);
  
  const option = CHART_OPTIONS.find(opt => opt.value === type);
  if (!option) {
    return <div>Aucun graphique disponible</div>;
  }

  if (option.type === 'pie') {
    return (
      <PieChart>
        <Pie
          data={data}
          dataKey="value"
          nameKey="name"
          cx="50%"
          cy="50%"
          outerRadius={150}
          label={({ name, percent }: PieLabelProps) => `${name} (${(percent * 100).toFixed(0)}%)`}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value: number) => [`${value} patients`, 'Nombre']} />
        <Legend />
      </PieChart>
    );
  }

  return (
    <BarChart data={data}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis 
        dataKey="name" 
        angle={-45} 
        textAnchor="end" 
        height={70}
        label={{ value: option.label, position: 'insideBottom', offset: -5 }}
      />
      <YAxis label={{ value: 'Nombre', angle: -90, position: 'insideLeft' }} />
      <Tooltip formatter={(value: number) => [`${value} ${type === 'consultations' ? 'consultations' : 'patients'}`, 'Nombre']} />
      <Bar dataKey="value" fill={COLORS[0]} name={option.label} />
    </BarChart>
  );
}

export default function StatistiquesPage() {
  const [selectedChart, setSelectedChart] = useState<ChartType>('wilaya');
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      try {
        console.log('Fetching stats...');
        const data = await getStats();
        console.log('Stats received:', data);
        setStats(data);
      } catch (err) {
        console.error('Error fetching stats:', err);
        setError('Erreur lors du chargement des statistiques');
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">Chargement des statistiques...</div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl text-red-500">{error || 'Erreur lors du chargement des statistiques'}</div>
      </div>
    );
  }

  console.log('Processing stats for display:', stats);

  const wilayaData = Object.entries(stats.wilayaStats)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => (b.value as number) - (a.value as number));

  const localisationData = Object.entries(stats.localisationStats)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => (b.value as number) - (a.value as number));

  const genreData = Object.entries(stats.genreStats)
    .map(([name, value]) => ({ name, value }));

  const ageData = Object.entries(stats.ageStats)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => parseInt(a.name) - parseInt(b.name));

  const consultationsData = Object.entries(stats.consultationsParMois)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => new Date(a.name).getTime() - new Date(b.name).getTime());

  const totalPatients = Object.values(stats.genreStats).reduce((a: number, b: number) => a + b, 0);
  const totalConsultations = Object.values(stats.consultationsParMois).reduce((a: number, b: number) => a + b, 0);

  const chartData = {
    wilaya: wilayaData,
    localisation: localisationData,
    genre: genreData,
    age: ageData,
    consultations: consultationsData,
  };

  console.log('Chart data for selected type:', selectedChart, chartData[selectedChart]);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Statistiques</h1>
        <div className="flex gap-4">
          <Select 
            defaultValue={selectedChart} 
            onValueChange={(value: ChartType) => {
              console.log('Chart type changed to:', value);
              setSelectedChart(value);
            }}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Sélectionner un graphique" />
            </SelectTrigger>
            <SelectContent>
              {CHART_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Durée moyenne des traitements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-center">
              {stats.dureeMoyenne} jours
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Nombre total de patients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-center">
              {totalPatients}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Nombre total de consultations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-center">
              {totalConsultations}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{CHART_OPTIONS.find(opt => opt.value === selectedChart)?.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              {renderChart(selectedChart, chartData[selectedChart])}
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 