import { create } from 'zustand';

interface Patient {
  id: number;
  nom: string;
  prenom: string;
  numero_patient: string;
  photo?: string;
  telephone?: string;
  date_naissance?: string;
  genre?: string;
  antecedent?: string;
  diagnostic?: string;
  lieu_naissance?: string;
  wilaya?: string;
  adresse?: string;
  localisation_cancer?: string;
  service_correspondant?: string;
  cni?: string;
  ct_sim?: string;
  debut_traitement?: string;
  fin_traitement?: string;
  rdv_traitement?: string;
  consultation?: string;
  consultation_specialisee?: string;
  technique_irradiation?: string;
  dose_totale?: number;
  dose_fraction?: number;
  cree_le?: string;
}

interface Medecin {
  id: number;
  nom: string;
  prenom: string;
}

interface Consultation {
  id: number;
  patientId: number;
  medecinId: number;
  patient: Patient;
  medecin: Medecin;
  date: Date;
  diagnostic?: string;
  traitement?: string;
  notes?: string;
  type?: 'INITIAL' | 'STANDARD' | 'SPECIALISEE';
}

interface ConsultationStore {
  consultation: Consultation | null;
  setConsultation: (consultation: Consultation | null) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export const useConsultation = create<ConsultationStore>((set) => ({
  consultation: null,
  isLoading: false,
  setConsultation: (consultation) => set({ consultation }),
  setIsLoading: (loading) => set({ isLoading: loading }),
}));






