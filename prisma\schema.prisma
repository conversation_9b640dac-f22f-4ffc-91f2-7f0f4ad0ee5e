generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           Int      @id @default(autoincrement())
  nom          String
  prenom       String
  email        String   @unique
  mot_de_passe String
  role         Role
  photo        String?
  cree_le      DateTime @default(now())
  medecin      Medecin?
}

model Medecin {
  id            Int                      @id @default(autoincrement())
  specialite    String
  telephone     String?
  userId        Int                      @unique
  consultations Consultation[]
  fileAttente   FileAttente[]
  historique    HistoriqueConsultation[]
  user          User                     @relation(fields: [userId], references: [id])
  messages      Message[]                @relation("MedecinMessages")
  patients      Patient[]                @relation("MedecinPatients")
}

model Patient {
  id                       Int                      @id @default(autoincrement())
  numero_patient           String                   @unique
  nom                      String
  prenom                   String
  telephone                String?
  date_naissance           DateTime
  genre                    Genre
  photo                    String?
  consultation             DateTime?
  consultation_specialisee DateTime?
  ct_sim                   DateTime?
  debut_traitement         DateTime?
  fin_traitement           DateTime?
  rdv_traitement           DateTime?
  technique_irradiation    String?
  dose_totale              Float?
  dose_fraction            Float?
  cree_le                  DateTime                 @default(now())
  adresse                  String?
  antecedent               String?
  diagnostic               String?
  medecinId                Int?
  consultations            Consultation[]
  fileAttentes             FileAttente[]
  historique               HistoriqueConsultation[]
  messages                 Message[]                @relation("PatientMessages")
  medecin                  Medecin?                 @relation("MedecinPatients", fields: [medecinId], references: [id])
}

model FileAttente {
  id        Int               @id @default(autoincrement())
  statut    StatutFileAttente @default(EN_ATTENTE)
  ordre     Int
  ajoute_le DateTime          @default(now())
  medecinId Int
  patientId Int
  medecin   Medecin           @relation(fields: [medecinId], references: [id])
  patient   Patient           @relation(fields: [patientId], references: [id])

  @@unique([patientId, medecinId, statut])
}

model Consultation {
  id         Int              @id @default(autoincrement())
  patientId  Int
  medecinId  Int
  date       DateTime         @default(now())
  diagnostic String?
  traitement String?
  notes      String?
  type       TypeConsultation @default(STANDARD)
  cree_le    DateTime         @default(now())
  debut      DateTime         @default(now())
  fin        DateTime?
  medecin    Medecin          @relation(fields: [medecinId], references: [id])
  patient    Patient          @relation(fields: [patientId], references: [id])
}

model Message {
  id        Int      @id @default(autoincrement())
  contenu   String
  date      DateTime @default(now())
  medecinId Int
  patientId Int
  type      String   @default("observation")
  medecin   Medecin  @relation("MedecinMessages", fields: [medecinId], references: [id])
  patient   Patient  @relation("PatientMessages", fields: [patientId], references: [id])
}

model HistoriqueConsultation {
  id        Int              @id @default(autoincrement())
  action    ActionHistorique
  date      DateTime         @default(now())
  medecinId Int
  patientId Int
  medecin   Medecin          @relation(fields: [medecinId], references: [id])
  patient   Patient          @relation(fields: [patientId], references: [id])
}

enum Role {
  admin
  medecin
  super_admin
}

enum Genre {
  Homme
  Femme
  Autre
}

enum StatutFileAttente {
  EN_ATTENTE
  EN_COURS
}

enum TypeConsultation {
  INITIAL
  STANDARD
  SPECIALISEE
}

enum ActionHistorique {
  AJOUT_FILE
  CONSULTATION
  FIN_TRAITEMENT
  AUTRE
}
