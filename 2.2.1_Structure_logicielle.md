## 2.2.1 Structure logicielle

Next.js 14 est conçu pour favoriser la séparation des préoccupations (SoC) et la maintenabilité. Cela signifie que le code est organisé de manière à ce que chaque composant ou fichier ait une responsabilité bien définie. Cela rend le code plus facile à comprendre, à modifier et à maintenir. Notre application web ELhassi 3.0 est structurée de la manière suivante :

### Dossier (app) :
Le dossier contient les pages suivantes (se formant des sous-dossiers) :

- **Page d'accueil (/)** : Tableau de bord administratif principal présentant une vue d'ensemble du système hospitalier avec des statistiques en temps réel sur les patients, rendez-vous et médecins. Affiche également une liste des patients récemment ajoutés et des consultations en cours.

- **Page de gestion des patients (/patients)** : Permet de visualiser, rechercher et filtrer la liste complète des patients. Offre des fonctionnalités d'ajout, de modification et de consultation des dossiers patients avec une interface intuitive et réactive.

- **Page de profil patient (/patients/[id])** : Affiche les informations détaillées d'un patient spécifique, incluant ses données personnelles, son historique médical, ses rendez-vous passés et à venir, et ses consultations. Permet également de modifier les informations du patient et de l'assigner à un médecin.

- **Page de gestion des médecins (/medecins)** : Interface complète pour gérer les médecins de l'établissement, avec possibilité d'ajouter de nouveaux médecins, de modifier leurs informations et de visualiser leurs statistiques de performance.

- **Page de profil médecin (/medecins/[id])** : Présente le profil détaillé d'un médecin avec ses informations personnelles, sa spécialité, ses statistiques de consultations, et la liste de ses patients assignés. Offre également un aperçu de son historique de consultations récentes.

- **Page d'administration (/administration)** : Espace réservé aux super-administrateurs pour gérer les utilisateurs administratifs du système, avec contrôle des permissions et des rôles.

- **Page de connexion (/login)** : Interface d'authentification sécurisée avec gestion des sessions et redirection intelligente selon le rôle de l'utilisateur. Intègre un écran de chargement optimisé qui précompile les composants nécessaires en fonction du rôle de l'utilisateur.

- **Page d'inscription (/register)** : Formulaire d'inscription pour les nouveaux utilisateurs avec validation des données et création de compte.

- **Interface médecin (/medecin/dashboard)** : Tableau de bord spécifique aux médecins, affichant leur file d'attente de patients, le patient actuellement en consultation, et des statistiques sur leurs activités quotidiennes.

- **Gestion des patients par médecin (/medecin/patients)** : Permet au médecin de consulter la liste de ses patients assignés et d'accéder à leurs dossiers médicaux.

- **Profil patient pour médecin (/medecin/patients/[id])** : Vue détaillée d'un patient spécifique adaptée aux besoins du médecin, avec focus sur les informations médicales et l'historique des consultations.

### Structure des routes et groupes de routes :

L'application utilise le système de groupes de routes de Next.js 14 pour organiser logiquement les différentes interfaces :

- **Groupe (admin)** : Contient toutes les pages accessibles aux administrateurs et super-administrateurs, avec une mise en page commune incluant une barre latérale de navigation et un en-tête.

- **Groupe (full-width-pages)** : Pages utilisant toute la largeur de l'écran sans barre latérale, comme les pages d'authentification.

- **Groupe (auth)** : Pages liées à l'authentification (connexion, inscription) avec une mise en page spécifique.

### Modèles de données principaux :

L'application s'appuie sur une base de données PostgreSQL gérée via Prisma ORM, avec les modèles suivants :

- **User** : Stocke les informations des utilisateurs du système (administrateurs, médecins) avec leurs rôles et informations d'authentification.

- **Medecin** : Contient les informations spécifiques aux médecins, liées à un utilisateur.

- **Patient** : Stocke les données des patients, incluant informations personnelles et médicales.

- **FileAttente** : Gère la file d'attente des patients pour chaque médecin.

- **Consultation** : Enregistre les consultations médicales avec leurs détails.

- **Message** : Permet la communication et la prise de notes entre médecins concernant les patients.

- **HistoriqueConsultation** : Conserve l'historique des actions liées aux consultations pour la traçabilité.

### Architecture des composants :

L'application suit une architecture moderne basée sur les composants React avec séparation claire entre :

- **Composants serveur** : Gèrent le rendu initial et la récupération des données côté serveur.
  
- **Composants client** : Gèrent l'interactivité et les mises à jour dynamiques de l'interface.

- **Composants partagés** : Éléments d'interface réutilisables comme les modales, formulaires et cartes.

- **Hooks personnalisés** : Encapsulent la logique métier et les appels API pour une meilleure réutilisation.

- **Contextes** : Gèrent l'état global de l'application (authentification, thème, barre latérale).

Cette architecture modulaire et bien structurée permet une maintenance facilitée et une évolution progressive du système, tout en offrant des performances optimales grâce à la précompilation des composants et au chargement intelligent des ressources.
